import base64
import os
import sys

import re
import json

import requests

RELEASE_MANAGER = os.environ.get('RELEASE_MANAGER')
SYSTEM_PULLREQUEST_PULLREQUESTID = os.environ.get(
    'SYSTEM_PULLREQUEST_PULLREQUESTID')
SYSTEM_PULLREQUEST_SOURCECOMMITID = os.environ.get(
    'SYSTEM_PULLREQUEST_SOURCECOMMITID')
BUILD_REPOSITORY_ID = os.environ.get('BUILD_REPOSITORY_ID')
TEAM_PROJECT = os.environ.get('SYSTEM_TEAMPROJECT')
EMAIL = os.environ.get('EMAIL')
JIRA_TOKEN = os.environ.get('JIRA_TOKEN')
PR_TARGET_BRANCH = os.environ.get('SYSTEM_PULLREQUEST_TARGETBRANCHNAME')


JIRA_STATUS_SCHEMA = {
    "DEV": "DEV",
    "UAT": "UAT",
    "PREPROD": "PREPROD",
    "MAIN": "PROD",
    "PROD": "PROD"
}


def pullRequest_completion(Error, status):
    e = Error
    comment_content = ""
    if e:
        for error in e:
            comment_content += f"> **type: {error['type']}**\n> \n > **file: {error['file']}**\n> \n> **message:** `{error['message']}`\n> \n> ---"

    ADD_REVIEWER_URL = f"https://edenred-ehq-salesforce.visualstudio.com/{TEAM_PROJECT}/_apis/git/repositories/{BUILD_REPOSITORY_ID}/pullrequests/{SYSTEM_PULLREQUEST_PULLREQUESTID}/reviewers/{RELEASE_MANAGER}?api-version=7.1-preview.1"
    COMPLETE_MERGE_URL = f"https://edenred-ehq-salesforce.visualstudio.com/{TEAM_PROJECT}/_apis/git/repositories/{BUILD_REPOSITORY_ID}/pullrequests/{SYSTEM_PULLREQUEST_PULLREQUESTID}?api-version=7.1-preview.1"
    ADD_COMMENT_URL = f"https://edenred-ehq-salesforce.visualstudio.com/{TEAM_PROJECT}/_apis/git/repositories/{BUILD_REPOSITORY_ID}/pullrequests/{SYSTEM_PULLREQUEST_PULLREQUESTID}/threads?api-version=7.1-preview.1"
    PR_INFO = f"https://edenred-ehq-salesforce.visualstudio.com/{TEAM_PROJECT}/_apis/git/repositories/{BUILD_REPOSITORY_ID}/pullrequests/{SYSTEM_PULLREQUEST_PULLREQUESTID}?api-version=7.1"

    ACCESS_TOKEN = os.environ.get('SYSTEM_ACCESSTOKEN')
    base64_token = base64.b64encode(
        f":{ACCESS_TOKEN}".encode("utf-8")).decode("utf-8")

    headers = {
        "User-Agent": "python/application",
        # "Accept-Encoding": "gzip, deflate",
        "Accept": "*/*",
        "Connection": "keep-alive",
        "Authorization": f"Basic {base64_token}",
        "Content-Type": "application/json",
    }
    if status == 'succeeded':
        ADD_REVIEWER_BODY = {
            'vote': 10,
            "isFlagged": False,
            "hasDeclined": False
        }
        COMPLETE_MERGE_BODY = {
            "autoCompleteSetBy": {
                "id": str(RELEASE_MANAGER)
            },
            "completionOption": {
                "mergeCommitMessage": "Merged by automation",
                "squashMerge": False,
                "deleteSourceBranch": False,
                "bypassPolicy": True,
                "autoComplete": True
            }
        }

    else:
        ADD_REVIEWER_BODY = {
            'vote': -10,
            "isFlagged": False,
            "hasDeclined": False
        }
        COMPLETE_MERGE_BODY = {
            "status": "abandoned",
            "closedBy": {
                "id": str(RELEASE_MANAGER),
            },
        }

    ADD_REVIEWER = requests.put(
        ADD_REVIEWER_URL, json=ADD_REVIEWER_BODY, headers=headers)
    COMPLETE_MERGE = requests.patch(
        COMPLETE_MERGE_URL, json=COMPLETE_MERGE_BODY, headers=headers)

    # Only add comment if there are errors
    if e:
        COMMENT_BODY = {
            "comments": [
                {
                    "parentCommentId": 0,
                    "content": str(comment_content),
                    "commentType": 1
                }
            ],
        }
        ADD_COMMENT = requests.post(
            ADD_COMMENT_URL, json=COMMENT_BODY, headers=headers)
        if ADD_COMMENT.status_code == 200:
            print(
                f"Deployment Errors Noted in Pull Request : #{SYSTEM_PULLREQUEST_PULLREQUESTID}")
        else:
            print("Comment could not be added")

    try:
        print(ADD_REVIEWER.json())
        sys.stdout.flush()
        # if ADD_REVIEWER.status_code == 200:
        reviewers_json = ADD_REVIEWER.json()
        reviewers = reviewers_json.get('reviewers', [])
        for reviewer in reviewers:
            display_name = reviewer.get('displayName')
            print(f"✅ Reviewer Added: {display_name}")
            sys.stdout.flush()
    except Exception as e:
        print(f"⛔ Failed to add Reviewer due to : \n {str(e)}")
        sys.stdout.flush()

    if COMPLETE_MERGE.status_code == 200:
        try:
            print(COMPLETE_MERGE.json())
            print("✅ Merge completed successfully")
            sys.stdout.flush()
        except requests.exceptions.JSONDecodeError:
            print("✅ Merge completed successfully (no JSON response)")
            sys.stdout.flush()
    else:
        print(
            f"\n ⛔ Merge not completed successfully. Status code: {COMPLETE_MERGE.status_code}")
        print(f"Response: {COMPLETE_MERGE.text}")
        sys.stdout.flush()

    # #Change status on JIRA
    try:
        pr_info_response = requests.get(PR_INFO, headers=headers)
        if pr_info_response.status_code == 200:
            PR_INFO_JSON = pr_info_response.json()
        else:
            print(
                f"⛔ Failed to get PR info. Status code: {pr_info_response.status_code}")
            print(f"Response: {pr_info_response.text}")
            return
    except requests.exceptions.JSONDecodeError:
        print("⛔ Failed to parse PR info response as JSON")
        print(f"Response text: {pr_info_response.text}")
        return
    except Exception as e:
        print(f"⛔ Error getting PR info: {str(e)}")
        return

    # Now access the 'title' field safely
    if "title" in PR_INFO_JSON:
        title = PR_INFO_JSON["title"]
        print(f"Ticket number is: {title}")
    else:
        print("Title not found in the response")
        return

    sys.stdout.flush()
    match = re.search(r"SFG-\d+", title)

    # Extract and store the ticket number if found
    TICKET_NUMBER = match.group() if match else None

    if not TICKET_NUMBER:
        print("⛔ No JIRA ticket number found in PR title. Skipping JIRA status update.")
        return

    JIRA_HEADER = {
        "User-Agent": "python/application",
        # "Accept-Encoding": "gzip, deflate",
        "Accept": "*/*",
        "Connection": "keep-alive",
        "Authorization": f"Basic {JIRA_TOKEN}",
        "Content-Type": "application/json",
    }
    JIRA_AUTH = (EMAIL, JIRA_TOKEN)

    Stage = PR_TARGET_BRANCH.upper()
    JIRA_STATUS = JIRA_STATUS_SCHEMA.get(Stage)

    print(f"target branch : {JIRA_STATUS}")
    GET_CURRENT_STATUS_URL = f"https://edenred.atlassian.net/rest/api/3/issue/{TICKET_NUMBER}?fields=status"

    try:
        status_response = requests.get(
            GET_CURRENT_STATUS_URL, auth=JIRA_AUTH, headers=JIRA_HEADER)
        if status_response.status_code == 200:
            STATUS_RESPONSE = status_response.json()
        else:
            print(
                f"⛔ Failed to get JIRA status. Status code: {status_response.status_code}")
            print(f"Response: {status_response.text}")
            return
    except requests.exceptions.JSONDecodeError:
        print("⛔ Failed to parse JIRA status response as JSON")
        print(f"Response text: {status_response.text}")
        return
    except Exception as e:
        print(f"⛔ Error getting JIRA status: {str(e)}")
        return

    try:
        transition_response = requests.get(
            f"https://edenred.atlassian.net/rest/api/3/issue/{TICKET_NUMBER}/transitions", auth=JIRA_AUTH, headers=JIRA_HEADER)
        if transition_response.status_code == 200:
            AVAILABLE_TRANSITION = transition_response.json()
        else:
            print(
                f"⛔ Failed to get JIRA transitions. Status code: {transition_response.status_code}")
            print(f"Response: {transition_response.text}")
            return
    except requests.exceptions.JSONDecodeError:
        print("⛔ Failed to parse JIRA transitions response as JSON")
        print(f"Response text: {transition_response.text}")
        return
    except Exception as e:
        print(f"⛔ Error getting JIRA transitions: {str(e)}")
        return

    try:
        CURRENT_STATUS = STATUS_RESPONSE["fields"]["status"]["name"]
    except KeyError as e:
        print(f"⛔ Failed to extract status from JIRA response: {str(e)}")
        print(f"Response structure: {STATUS_RESPONSE}")
        return

    if CURRENT_STATUS.upper() == JIRA_STATUS:
        print(
            f"ticket already in {JIRA_STATUS}, this must be a FIX or UPDATE \n ticket status not changed on JIRA")
    else:
        GET_TRANSITION_ID = next(
            (t["id"] for t in AVAILABLE_TRANSITION["transitions"] if t["name"] == JIRA_STATUS), None)

        CHANGE_STATUS_URL = f"https://edenred.atlassian.net/rest/api/3/issue/{TICKET_NUMBER}/transitions"
        CHANGE_STATUS_BODY = {
            "transition": {
                "id": GET_TRANSITION_ID
            }
        }

        CHANGE_STATUS = requests.post(
            CHANGE_STATUS_URL, json=CHANGE_STATUS_BODY, auth=JIRA_AUTH, headers=JIRA_HEADER)

        if CHANGE_STATUS.status_code in [200, 204]:
            print(
                f"✅ Ticket status has been changed on JIRA to {JIRA_STATUS} with the transition ID: {GET_TRANSITION_ID}")
            try:
                if CHANGE_STATUS.text.strip():  # Only try to parse JSON if there's content
                    print(CHANGE_STATUS.json())
            except requests.exceptions.JSONDecodeError:
                pass  # Some successful responses don't return JSON
            sys.stdout.flush()
        else:
            print(
                f"⛔ Failed to change ticket status. Status code: {CHANGE_STATUS.status_code}")
            print(f"Response: {CHANGE_STATUS.text}")
            sys.stdout.flush()
