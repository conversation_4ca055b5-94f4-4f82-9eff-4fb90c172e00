import os
import sys
import time
import zipfile

from simple_salesforce import Salesforce

from TokenGen import generat_token
from az import pullRequest_completion


def zipdir(dir, ziph):
    for root, dirs, files in os.walk(dir):
        for file in files:
            rel_path = os.path.relpath(os.path.join(root, file), dir)
            ziph.write(os.path.join(root, file), rel_path)


def deploy_to_salesforce():
    try:
        BUILD_REQUESTEDFOREMAIL = os.environ.get('BUILD_REQUESTEDFOREMAIL')
        deployment_finished = False
        successful = False
        Error = []
        start_time = time.time()
        timeout = 3600  # 1 hour timeout

        result = generat_token()
        access_token = result['access_token']
        instance = result['instance_url']
        sf = Salesforce(instance_url=instance, session_id=access_token)

        with zipfile.ZipFile('package.zip', 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipdir('build/src/', zipf)

        if not os.path.exists('package.zip') or os.path.getsize('package.zip') == 0:
            raise Exception("Package.zip is empty or not created")

        result = sf.deploy('package.zip', sandbox=True)
        asyncId = result.get('asyncId')
        print('\n📢 Deployment in progress ...')
        sys.stdout.flush()

        while not deployment_finished:
            if time.time() - start_time > timeout:
                raise Exception("Deployment timeout after 1 hour")

            result = sf.checkDeployStatus(asyncId)
            if result.get('state') in ["Succeeded", "Canceled", "Canceling"]:
                deployment_finished = True
                successful = True
            if result.get('state') in ["SucceededPartial", "Failed"]:
                deployment_finished = True
                successful = False
                Error = result.get("deployment_detail", {}).get("errors", [])
            if result.get('state') in ["Pending", "InProgress"]:
                deployment_finished = False
                successful = False
                print(f"\n 📢 {result}")
                sys.stdout.flush()
            sys.stdout.flush()
            time.sleep(1)

        print("📢 Deployment status:")
        sys.stdout.flush()
        if successful:
            print("Succeded ✅")
            sys.stdout.flush()
            # generate_html(Error, BUILD_REQUESTEDFOREMAIL, True)
            pullRequest_completion(Error=None, status='succeeded')

        else:
            print("Failed ⛔")
            sys.stdout.flush()
            # generate_html(Error, BUILD_REQUESTEDFOREMAIL, False)
            pullRequest_completion(Error=Error, status='failed')
    except Exception as e:
        print(f"⛔ Deployment failed: {str(e)}")
        sys.stdout.flush()
        raise
